import React, { useState, useCallback, useMemo, useEffect } from "react";
import { X, Plus } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import SplitCardIcon from "./split-card-icon";
import { useGetPlayerList } from "@/api/booking-checkout-service";
import { useAuthStore } from "@/store/auth-store";
import { toast } from "sonner";
import { useBookingStore } from "@/store/booking-store";
import PlayerManagementDialog from "./player-management-dialog";
import { useCheckoutStore } from "@/store/checkout-store";
import { BookingEstimateData } from "./summary-section";
import { Skeleton } from "../ui/skeleton";

// Player interface for the split payment
interface Player {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  country_code?: string;
  isSelected: boolean;
  isCurrentUser?: boolean;
}

type PayType = "pay-your-part" | "pay-everything";

interface PaySplitSectionProps {
  estimateData: BookingEstimateData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

const PaySplitSection = ({ estimateData, isLoading, error, refetch }: PaySplitSectionProps) => {
  const { user } = useAuthStore();
  const [isPlayerDialogOpen, setIsPlayerDialogOpen] = useState(false);

  const { courtBookingDetails, addGuestUsers, setCourtBookingDetails } = useBookingStore();
  const [selectedPayType, setSelectedPayType] = useState<PayType>(
    courtBookingDetails?.split ? "pay-your-part" : "pay-everything"
  );
  const { lastTimetoPay } = useCheckoutStore();

  // Maximum players allowed (including current user)
  const MAX_PLAYERS = 3; // Total players including current user
  const MIN_PLAYERS = 1; // Minimum players including current user

  // Use the player list hook
  const { playerList, playerListLoading, revalidateGetPlayerList } = useGetPlayerList();

  const payTypes = [
    { value: "pay-your-part" as PayType, name: "Pay your part" },
    { value: "pay-everything" as PayType, name: "Pay everything" },
  ];

  // Create players list based on store data and API data
  const players = useMemo(() => {
    if (playerListLoading) return [];

    try {
      // Get existing guest user IDs from booking store
      const existingGuestUserIds = courtBookingDetails?.guest_users || [];

      // // Create current user player (always selected and cannot be deselected)
      // const currentUserPlayer: Player = {
      //   id: user?.id?.toString() || "current-user",
      //   name: user?.name || "You",
      //   email: user?.user_email?.email,
      //   phone: user?.user_phone?.phone,
      //   country_code: user?.user_phone?.country_code,
      //   isSelected: true, // Current user is always selected
      //   isCurrentUser: true,
      // };

      // Create players list from API players (excluding current user)
      const apiPlayers: Player[] = (Array.isArray(playerList) ? playerList : [])
        .filter((player: any) => {
          const playerId = player.id?.toString() || player.user_id?.toString();
          return playerId !== user?.id?.toString(); // Exclude current user from API players
        })
        .map(
          (player: any): Player => ({
            id: player.id?.toString() || player.user_id?.toString(),
            name: player.name || player.user?.name,
            email: player.user_email?.email,
            phone: player.user_phone?.phone,
            country_code: player.user_phone?.country_code,
            isSelected: existingGuestUserIds.includes(
              parseInt(player.id?.toString() || player.user_id?.toString())
            ),
            isCurrentUser: false,
          })
        );

      // Combine current user with API players (current user first)
      return [...apiPlayers];
    } catch (error) {
      console.error("Failed to load players:", error);
      // Fallback to just current user
      const currentUserPlayer: Player = {
        id: user?.id?.toString() || "current-user",
        name: user?.name || "You",
        email: user?.user_email?.email,
        phone: user?.user_phone?.phone,
        country_code: user?.user_phone?.country_code,
        isSelected: true,
        isCurrentUser: true,
      };
      return [currentUserPlayer];
    }
  }, [user, playerList, playerListLoading, courtBookingDetails?.guest_users]);

  useEffect(() => {
    console.log("courtBookingDetails?.split", courtBookingDetails?.split);
    if (courtBookingDetails?.split) {
      setSelectedPayType("pay-your-part");
    } else {
      setSelectedPayType("pay-everything");
    }
  }, []);

  // Handle payment type change
  const handlePayTypeChange = useCallback(
    (payType: PayType) => {
      setSelectedPayType(payType);

      // Update booking store split flag
      if (courtBookingDetails) {
        setCourtBookingDetails({
          ...courtBookingDetails,
          split: payType === "pay-your-part",
        });
      }

      // If switching to "pay everything", clear guest users
      if (payType === "pay-everything") {
        addGuestUsers([]);
      }
    },
    [courtBookingDetails, setCourtBookingDetails, addGuestUsers]
  );

  // Get selected players count and display info
  const selectedPlayers = useMemo(() => players.filter((player) => player.isSelected), [players]);

  const handleOpenPlayerDialog = useCallback(() => {
    setIsPlayerDialogOpen(true);
  }, []);

  const handleClosePlayerDialog = useCallback(() => {
    setIsPlayerDialogOpen(false);
  }, []);

  const handlePlayerToggle = useCallback(
    (playerId: string) => {
      const currentSelected = selectedPlayers.length;
      const playerToToggle = players.find((p) => p.id === playerId);

      if (!playerToToggle) return;

      // Current user cannot be deselected
      if (playerToToggle.isCurrentUser) {
        toast.error("You cannot deselect yourself");
        return;
      }

      // If trying to select a player and we're at max capacity, prevent selection
      if (!playerToToggle.isSelected && currentSelected >= MAX_PLAYERS) {
        toast.error(`Maximum ${MAX_PLAYERS} players allowed`);
        return;
      }

      // If trying to deselect and we're at minimum capacity, prevent deselection
      // if (playerToToggle.isSelected && currentSelected <= MIN_PLAYERS) {
      //   toast.error(`Minimum ${MIN_PLAYERS} players required for split payment`);
      //   return;
      // }

      // Update guest users in the store
      const currentGuestUsers = courtBookingDetails?.guest_users || [];
      const playerId_num = parseInt(playerId);

      if (isNaN(playerId_num)) return;

      let updatedGuestUsers: number[];
      if (playerToToggle.isSelected) {
        // Remove player from guest users
        updatedGuestUsers = currentGuestUsers.filter((id) => id !== playerId_num);
      } else {
        // Add player to guest users
        updatedGuestUsers = [...currentGuestUsers, playerId_num];
      }

      addGuestUsers(updatedGuestUsers);
    },
    [
      players,
      selectedPlayers,
      courtBookingDetails?.guest_users,
      addGuestUsers,
      MAX_PLAYERS,
      MIN_PLAYERS,
    ]
  );

  const selectedPlayersCount = selectedPlayers.length;

  const payYourPartAmout = useMemo(() => {
    if (!estimateData) return 0;
    if (estimateData.split) {
      return estimateData.my_booking_base_price.toFixed(2);
    } else {
      return (estimateData.my_booking_base_price / 4).toFixed(2);
    }
  }, [estimateData]);

  const payEverythingAmount = useMemo(() => {
    if (!estimateData) return 0;
    if (estimateData.split) {
      if (estimateData.is_free_booking) {
        return estimateData.hold_amount.toFixed(2);
      }
      return (estimateData.my_booking_base_price * 4).toFixed(2);
    } else {
      if (estimateData.is_free_booking) {
        return typeof estimateData.booking_price === "number"
          ? estimateData.booking_price.toFixed(2)
          : parseFloat(estimateData.booking_price).toFixed(2);
      }
      return estimateData.my_booking_base_price.toFixed(2);
    }
  }, [estimateData]);

  console.log("estimated data", estimateData);
  console.log("payYourPartAmout", payYourPartAmout);
  console.log("payEverythingAmount", payEverythingAmount);

  return (
    <div className="w-full space-y-4">
      {/* Pay Type Radio Buttons */}
      <div className="space-y-3">
        <RadioGroup
          value={selectedPayType}
          onValueChange={(value) => handlePayTypeChange(value as PayType)}
          className="space-y-2"
        >
          <div key={payTypes[0].value} className="flex items-center space-x-3">
            <RadioGroupItem value={payTypes[0].value} id={payTypes[0].value} className="h-5 w-5" />
            <label
              htmlFor={payTypes[0].value}
              className="flex w-full cursor-pointer items-center justify-between text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              <div className="flex items-center gap-2">
                <SplitCardIcon number={selectedPlayersCount + 1} color="#1C5534" />
                <span className="text-base font-medium">{payTypes[0].name}</span>
              </div>
              {isLoading ? (
                <Skeleton className="h-4 w-12" />
              ) : (
                <p className="font-helvetica text-base font-bold text-gray-500">
                  {"$" + (payYourPartAmout || "N/A")}
                </p>
              )}
            </label>
          </div>
          {lastTimetoPay && selectedPayType === "pay-your-part" && (
            <div className="rounded-m mt-2 p-1">
              <p className="text-xs text-amber-700">
                All players must complete payment by the deadline below. If any player fails to pay,
                the remaining amount will be automatically charged to your account.
              </p>
              <p className="mt-2 text-xs font-medium text-amber-900">
                Payment deadline: {lastTimetoPay}
              </p>
            </div>
          )}
          {/* Add Players Button - Only show when "Pay your part" is selected */}
          {selectedPayType === "pay-your-part" && (
            <div className="flex w-full flex-col items-center justify-center space-y-3">
              {selectedPlayersCount >= 1 && (
                <div className="w-full space-y-2">
                  {/* Display selected players */}
                  <div className="flex flex-col flex-wrap justify-center gap-2">
                    {selectedPlayers.map((player) => (
                      <div
                        key={player.id}
                        className="text-primary flex items-center justify-between gap-2 rounded-full px-3 py-1 text-sm"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                            <span className="text-primary text-sm font-medium">
                              {player.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="text-sm font-medium">{player.name}</p>
                            {player?.email && (
                              <p className="text-xs text-gray-500">{player.email}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          {/* <p className="font-helvetica text-base font-bold text-gray-500">
                            {"$" + (payYourPartAmout || "N/A")}
                          </p> */}
                          <button
                            onClick={() => handlePlayerToggle(player.id)}
                            className="flex h-6 w-6 items-center justify-center rounded-full bg-white text-black transition-colors hover:bg-gray-100"
                          >
                            <X className="h-4 w-4 text-gray-300" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <button
                onClick={handleOpenPlayerDialog}
                className="text-primary flex items-center gap-2 rounded-full px-4 py-2 transition-colors"
              >
                <Plus className="h-4 w-4" />
                Add Players
              </button>
            </div>
          )}
          <div key={payTypes[1].value} className="flex items-center space-x-3">
            <RadioGroupItem value={payTypes[1].value} id={payTypes[1].value} className="h-5 w-5" />
            <label
              htmlFor={payTypes[1].value}
              className="flex w-full cursor-pointer items-center justify-between text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              <div className="flex items-center gap-2">
                <SplitCardIcon number={4} color="#DBB704" />
                <span className="text-base font-medium">{payTypes[1].name}</span>
              </div>
              {isLoading ? (
                <Skeleton className="h-4 w-12" />
              ) : (
                <p className="font-helvetica text-base font-bold text-gray-500">
                  {"$" + payEverythingAmount || "N/A"}
                </p>
              )}
            </label>
          </div>
        </RadioGroup>
      </div>

      {/* Player Management Dialog */}
      <PlayerManagementDialog
        isOpen={isPlayerDialogOpen}
        onClose={handleClosePlayerDialog}
        players={players}
        onPlayerToggle={handlePlayerToggle}
        selectedPlayersCount={selectedPlayersCount}
        maxPlayers={MAX_PLAYERS}
        playerListLoading={playerListLoading}
        revalidateGetPlayerList={revalidateGetPlayerList}
      />
    </div>
  );
};

export default PaySplitSection;
