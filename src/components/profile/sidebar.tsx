"use client";

import { useAuthStore } from "@/store/auth-store";
import Image from "next/image";
import React from "react";

interface SidebarProps {
  activeSection: string;
  handleSectionClick: (sectionId: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeSection, handleSectionClick }) => {
  const { user } = useAuthStore();

  return (
    <aside className="w-full rounded-lg bg-white p-4 md:w-80">
      <div
        className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
          activeSection === "profile-section" ? "bg-primary/10 text-primary" : "hover:bg-gray-50"
        }`}
        onClick={() => handleSectionClick("profile-section")}
      >
        <div className="mr-3 h-8 w-8">
          <Image
            src="/imgs/profile-lo.png"
            alt="profile"
            title="profile"
            className="h-full w-full object-cover"
            width={32}
            height={32}
            priority
          />
        </div>
        <div>
          <div className="font-medium">Profile</div>
          <div className="text-sm text-gray-500">Add your personal information</div>
        </div>
      </div>

      <div
        className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
          activeSection === "subscriptions-section"
            ? "bg-primary/10 text-primary"
            : "hover:bg-gray-50"
        }`}
        onClick={() => handleSectionClick("subscriptions-section")}
      >
        <div className="mr-3 h-8 w-8">
          <Image
            src="/imgs/subsc.png"
            alt="subscriptions"
            title="subscriptions"
            className="h-full w-full object-cover"
            width={32}
            height={32}
            priority
          />
        </div>
        <div>
          <div className="font-medium">My Subscriptions</div>
          <div className="text-sm text-gray-500">Manage your subscriptions</div>
        </div>
      </div>

      <div
        className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
          activeSection === "family-section" ? "bg-primary/10 text-primary" : "hover:bg-gray-50"
        }`}
        onClick={() => handleSectionClick("family-section")}
      >
        <div className="mr-3 h-8 w-8">
          <Image
            src="/imgs/profile-lo.png"
            alt="family"
            title="family"
            className="h-full w-full object-cover"
            width={32}
            height={32}
            priority
          />
        </div>
        <div>
          <div className="font-medium">{`${user?.is_corporate_user ? "Corporate Membership" : "Family Membership"}`}</div>
          <div className="text-sm text-gray-500">{`${user?.is_corporate_user ? (user?.is_corporate_root_user ? "Add your corporate members" : "See your corporate members") : "Add your family members"}`}</div>
        </div>
      </div>

      <div
        className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
          activeSection === "payment-methods-section"
            ? "bg-primary/10 text-primary"
            : "hover:bg-gray-50"
        }`}
        onClick={() => handleSectionClick("payment-methods-section")}
      >
        <div className="mr-3 h-8 w-8">
          <Image
            src="/imgs/subsc.png" // Replace with appropriate icon
            alt="payment methods"
            title="payment methods"
            className="h-full w-full object-cover"
            width={32}
            height={32}
            priority
          />
        </div>
        <div>
          <div className="font-medium">Payment Methods</div>
          <div className="text-sm text-gray-500">Manage your payment methods</div>
        </div>
      </div>

      <div
        className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
          activeSection === "bookings-section" ? "bg-primary/10 text-primary" : "hover:bg-gray-50"
        }`}
        onClick={() => handleSectionClick("bookings-section")}
      >
        <div className="mr-3 h-8 w-8">
          <Image
            src="/imgs/subsc.png" // Replace with appropriate icon
            alt="bookings"
            title="bookings"
            className="h-full w-full object-cover"
            width={32}
            height={32}
            priority
          />
        </div>
        <div>
          <div className="font-medium">My Bookings</div>
          <div className="text-sm text-gray-500">View your bookings</div>
        </div>
      </div>

      <div
        className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
          activeSection === "logout-section" ? "bg-primary/10 text-primary" : "hover:bg-gray-50"
        }`}
        onClick={() => handleSectionClick("logout-section")}
      >
        <div className="mr-3 h-8 w-8">
          <Image
            src="/imgs/logout.png"
            alt="logout"
            title="logout"
            className="h-full w-full object-cover"
            width={32}
            height={32}
            priority
          />
        </div>
        <div>
          <div className="font-medium">Logout</div>
          <div className="text-sm text-gray-500">:(</div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
