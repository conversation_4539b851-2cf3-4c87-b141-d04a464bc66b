"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import CourtBookingSection from "./bookings/my-court-booking-section";

interface BookingsSectionProps {
  activeSection: string;
}

const BookingsSection: React.FC<BookingsSectionProps> = ({ activeSection }) => {
  const [timeFilter, setTimeFilter] = useState<"upcoming" | "past">("upcoming");

  const handleBookingTimeSelector = (timeFilter: "upcoming" | "past") => {
    setTimeFilter(timeFilter);
    console.log("Booking time filter changed to", timeFilter);
  };

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "bookings-section" ? "" : "hidden"
      }`}
      id="bookings-section"
    >
      <h2 className="mb-6 text-2xl font-bold text-gray-900">My Bookings</h2>

      {/* <div className="mb-6 h-12 w-full max-w-md rounded-[100px] bg-neutral-100 p-1">
        <button
          onClick={() => handleBookingTimeSelector("upcoming")}
          className="font-helvetica h-full flex-1 rounded-[30px] text-sm font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
        >
          Upcoming
        </button>
        <button
          onClick={() => handleBookingTimeSelector("past")}
          className="font-helvetica h-full flex-1 rounded-[30px] text-sm font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
        >
          Past
        </button>
      </div> */}

      <Tabs defaultValue="court" className="flex w-full flex-col items-center">
        <TabsList className="mb-6 h-12 w-full max-w-xl rounded-[100px] bg-neutral-100 p-1">
          <TabsTrigger
            value="court"
            className="font-helvetica h-full flex-1 rounded-[30px] text-sm font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
          >
            Court
          </TabsTrigger>
          <TabsTrigger
            value="lesson"
            className="font-helvetica h-full flex-1 rounded-[30px] text-sm font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
          >
            Lesson
          </TabsTrigger>
          <TabsTrigger
            value="program"
            className="font-helvetica h-full flex-1 rounded-[30px] text-sm font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
          >
            Program
          </TabsTrigger>
        </TabsList>
        <TabsContent value="court" className="mt-0">
          <CourtBookingSection timeFilter={timeFilter} />
        </TabsContent>

        <TabsContent value="lesson" className="mt-0">
          <div>Lesson bookings content - {timeFilter}</div>
        </TabsContent>

        <TabsContent value="program" className="mt-0">
          <div>Program bookings content - {timeFilter}</div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BookingsSection;
