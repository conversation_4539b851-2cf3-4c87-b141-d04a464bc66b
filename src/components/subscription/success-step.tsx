"use client";
import React from "react";
import { CheckCircle, Calendar, CreditCard, Mail, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { PersonalInfo, PaymentMethod } from "./types";
import { Plan } from "@/types/membership";
import { useRouter } from "next/navigation";
import { useSubscriptionStore } from "@/store/subscription-store";

interface SuccessStepProps {
  selectedPlan: Plan | null;
  personalInfo: PersonalInfo;
  selectedPaymentMethod: PaymentMethod | null;
}

export default function SuccessStep({
  selectedPlan,
  personalInfo,
  selectedPaymentMethod,
}: SuccessStepProps) {
  const router = useRouter();
  const currentDate = new Date();
  const nextBillingDate = new Date(currentDate);
  nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);

  const { resetSubscription } = useSubscriptionStore();

  const handleBookFirstSession = async () => {
    router.push("/booking");
    setTimeout(() => {
      resetSubscription();
    }, 5000);
  };

  const handleViewMembershipDetails = async () => {
    router.push("/profile?section=membership");
    setTimeout(() => {
      resetSubscription();
    }, 5000);
  };

  return (
    <div className="w-full rounded-lg bg-gradient-to-br from-white to-[#FFFAED] p-6 lg:max-w-6xl">
      {/* Success Header */}
      <div className="mb-8 text-center">
        <div className="mb-4 flex justify-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-[#1C5534] to-[#0f3a1f]">
            <CheckCircle className="h-10 w-10 text-white" />
          </div>
        </div>
        <h2 className="mb-2 text-gray-900">Welcome to Epic Padel!</h2>
        <p className="text-gray-600">Your membership has been successfully activated</p>
      </div>

      {/* Membership Confirmation Card */}
      {selectedPlan && (
        <div className="mb-6 rounded-lg bg-gradient-to-r from-[#1C5534] to-[#0f3a1f] p-6 text-white">
          <div className="mb-4 flex items-start justify-between">
            <div>
              <h3 className="mb-1 text-white">Membership Confirmed</h3>
              <p className="mb-2 text-xl text-white">{selectedPlan.name}</p>
              {selectedPlan.name !== "Pay-to-Play" && (
                <div className="flex items-baseline space-x-1">
                  <span className="text-2xl text-white">
                    ${selectedPlan.package_prices.find((p) => p.frequency === "M")?.amount || 0}
                  </span>
                  <span className="text-sm text-green-100">/month</span>
                </div>
              )}
            </div>
            {selectedPlan.package_prices.some((p) => p.is_on_offer) && (
              <div className="rounded-full bg-gradient-to-r from-[#DEBA0C] to-[#B8960A] px-3 py-1 text-sm text-black">
                Founding Member
              </div>
            )}
          </div>

          {/* {selectedPlan.description && (
            <p className="mb-4 text-sm text-green-100">{selectedPlan.description}</p>
          )} */}

          {/* Membership Benefits */}
          <div className="border-t border-green-400/20 pt-4">
            <p className="mb-2 text-sm text-green-100">Your membership includes:</p>
            <ul className="space-y-1">
              {selectedPlan.package_benefits.slice(0, 3).map((benefit) => (
                <li key={benefit.id} className="flex items-start text-sm text-white">
                  <CheckCircle className="mt-0.5 mr-2 h-3 w-3 flex-shrink-0 text-green-200" />
                  <span className="leading-tight">{benefit.name}</span>
                </li>
              ))}
              {selectedPlan.package_benefits.length > 3 && (
                <li className="text-sm text-green-100">
                  + {selectedPlan.package_benefits.length - 3} more benefits
                </li>
              )}
            </ul>
          </div>
        </div>
      )}

      {/* Account & Billing Details */}
      <div className="mb-8 grid gap-6 md:grid-cols-2">
        {/* Account Details */}
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="mb-3 flex items-center">
            <User className="mr-2 h-4 w-4 text-[#1C5534]" />
            <h4 className="text-gray-900">Account Details</h4>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Name:</span>
              <span className="text-gray-900">{personalInfo.name || "Not provided"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Email:</span>
              <span className="text-gray-900">{personalInfo.email || "Not provided"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Mobile:</span>
              <span className="text-gray-900">{personalInfo.mobile || "Not provided"}</span>
            </div>
          </div>
        </div>

        {/* Billing Information */}
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="mb-3 flex items-center">
            <CreditCard className="mr-2 h-4 w-4 text-[#1C5534]" />
            <h4 className="text-gray-900">Billing Information</h4>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Method:</span>
              <span className="text-gray-900">
                {selectedPaymentMethod
                  ? `•••• ${selectedPaymentMethod.last4}`
                  : "Card ending in ••••"}
              </span>
            </div>
            {selectedPlan && selectedPlan.name !== "Pay-to-Play" && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-600">Next Billing:</span>
                  <span className="text-gray-900">{nextBillingDate.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="text-gray-900">
                    ${selectedPlan.package_prices.find((p) => p.frequency === "M")?.amount || 0}
                    /month
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="mb-8 rounded-lg border border-gray-200 bg-gradient-to-r from-white to-[#FFFAED] p-6">
        <div className="mb-3 flex items-center">
          <Calendar className="mr-2 h-4 w-4 text-[#1C5534]" />
          <h4 className="text-gray-900">What's Next?</h4>
        </div>
        <ul className="space-y-2 text-sm text-gray-700">
          <li className="flex items-start">
            <Mail className="mt-0.5 mr-2 h-3 w-3 flex-shrink-0 text-[#1C5534]" />
            <span>Check your email for a welcome message and account activation details</span>
          </li>
          <li className="flex items-start">
            <Calendar className="mt-0.5 mr-2 h-3 w-3 flex-shrink-0 text-[#1C5534]" />
            <span>Download the Epic Padel app to book your first court session</span>
          </li>
          <li className="flex items-start">
            <CheckCircle className="mt-0.5 mr-2 h-3 w-3 flex-shrink-0 text-[#1C5534]" />
            <span>Visit our location to collect your membership welcome pack</span>
          </li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col justify-center gap-3 sm:flex-row">
        <Button
          className="bg-gradient-to-r from-[#1C5534] to-[#0f3a1f] px-8 py-3 text-white transition-all duration-200 hover:from-[#0f3a1f] hover:to-[#1C5534]"
          onClick={handleBookFirstSession}
        >
          Book Your First Session
        </Button>
        <Button
          variant="outline"
          className="border-[#1C5534] px-8 py-3 text-[#1C5534] transition-all duration-200 hover:bg-[#1C5534] hover:text-white"
          onClick={handleViewMembershipDetails}
        >
          View Membership Details
        </Button>
      </div>
    </div>
  );
}
