import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { object } from "zod";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    day: "numeric",
    month: "long",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  };
  return date.toLocaleDateString("en-US", options);
};


export function extractErrorMessage(error: any) {

  console.log("error", error);

  if (error?.errors && typeof error?.errors === "string") {
    return error?.errors;
  }

  if (typeof error?.errors === "object" && error?.errors !== null) {
    const firstKey = Object.keys(error.errors)[0];
    const messages = error.errors[firstKey];
    console.log("firstKey", firstKey);
    if (Array.isArray(messages) && messages.length > 0) {
      return messages[0];
    }
    if (typeof messages === "string") {
      return messages;
    }
  }

  if (typeof error?.message === "object" && error?.message !== null) {
    const firstKey = Object.keys(error.message)[0];
    const messages = error.message[firstKey];
    if (Array.isArray(messages) && messages.length > 0) {
      return messages[0];
    }
    if (typeof messages === "string") {
      return messages;
    }
  }

  if (typeof error?.message === "string")
    return error?.message;

  return null;
}

export function modifyDate(date: string) {
  const d = new Date(date);
  if (isNaN(d.getTime())) {
    return "N/A";
  }
  const day = d.getDate();
  const month = d.toLocaleString("en-US", { month: "long" });
  const year = d.getFullYear();

  function getOrdinal(n: number) {
    if (n > 3 && n < 21) return "th";
    switch (n % 10) {
      case 1: return "st";
      case 2: return "nd";
      case 3: return "rd";
      default: return "th";
    }
  }

  return `${day}${getOrdinal(day)} ${month} ${year}`;
}

// Format program date range: "Apr 21st - Jun 9th (7 days)"
export function formatProgramDateRange(startDate: string, endDate: string): string {
  if (!startDate || !endDate) {
    return "Date range not available";
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return "Invalid date range";
  }

  // Calculate the difference in days
  const timeDiff = end.getTime() - start.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end days

  function getOrdinal(n: number) {
    if (n > 3 && n < 21) return "th";
    switch (n % 10) {
      case 1: return "st";
      case 2: return "nd";
      case 3: return "rd";
      default: return "th";
    }
  }

  const startDay = start.getDate();
  const startMonth = start.toLocaleString("en-US", { month: "short" });

  const endDay = end.getDate();
  const endMonth = end.toLocaleString("en-US", { month: "short" });

  return `${startMonth} ${startDay}${getOrdinal(startDay)} - ${endMonth} ${endDay}${getOrdinal(endDay)} (${daysDiff} days)`;
}

export function formatDateList(dates: string[]): string {
  if (!dates || dates.length === 0) {
    return "No dates available";
  }

  return dates.map((date) => {
    const d = new Date(date);
    if (isNaN(d.getTime())) {
      return "N/A";
    }
    const day = d.getDate();
    const month = d.toLocaleString("en-US", { month: "numeric" });
    return `${month}/${day}`;
  }).join(" - ");
}

// Format program time range: "5:00 pm - 7:00 pm"
export function formatProgramTimeRange(startTime: string, endTime: string): string {
  if (!startTime || !endTime) {
    return "Time not available";
  }

  try {
    // Parse the time strings (assuming they're in ISO format like "2025-07-18T12:10:00.000000Z")
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return "Invalid time range";
    }

    const formatTime = (date: Date) => {
      return date.toLocaleString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      }).toLowerCase();
    };

    return `${formatTime(start)} - ${formatTime(end)}`;
  } catch (error) {
    return "Time format error";
  }
}


export const getCardBrandIcon = (brand: string | null) => {
  switch (brand) {
    case "visa":
      return "https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg";
    case "mastercard":
      return "https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg";
    case "amex":
      return "https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6e418a6ca1717c.svg";
    case "discover":
      return "https://js.stripe.com/v3/fingerprinted/img/discover-ac52cd46f89fa40a29a0bfb954e33173.svg";
    default:
      return "https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg";
  }
};