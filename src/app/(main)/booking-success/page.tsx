import PrimaryLinkButton from "@/components/primary-link-button";
import Image from "next/image";
import React from "react";

const BookingSuccessPage = () => {
  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center gap-4 px-4 py-8">
      <div className="text-center">
        <h1 className="font-helvetica text-3xl font-bold lg:text-4xl">Thank You!</h1>
        <p className="text-lg">Thank you for your booking.</p>
      </div>
      <Image
        src="/imgs/success.gif"
        alt="Booking Success"
        width={500}
        height={500}
        className="size-[224px] rounded-xl"
      />

      <div className="line-clamp-1 flex max-w-md flex-col items-center justify-center gap-4 px-4 pb-8 text-center">
        <p className="text-lg">To manage your bookings,</p>
        <PrimaryLinkButton
          href="/profile?section=bookings"
          text="Download Epic Padel App"
          className="mt-2 w-full text-lg"
        />
        <p>Or</p>
        <PrimaryLinkButton
          href="/"
          text="Go back home"
          className="mt-2 w-full bg-[#F5F5F5] text-center text-lg text-[#969696] hover:bg-[#F5F5F5]/90"
        />
      </div>
    </div>
  );
};

export default BookingSuccessPage;
