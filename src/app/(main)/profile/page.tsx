"use client";

import UpdateEmailDialog from "@/components/dialogs/update-email-dialog";
import UpdatePhoneDialog from "@/components/dialogs/update-phone-dialog";
import ProtectedLayout from "@/components/layouts/protected-layout";
import BookingsSection from "@/components/profile/bookings-section";
import FamilySection from "@/components/profile/family-section";
import LogoutSection from "@/components/profile/logout-section";
import PaymentMethodsSection from "@/components/profile/payment-methods-section";
import ProfileSection from "@/components/profile/profile-section";
import Sidebar from "@/components/profile/sidebar";
import BookingsSkeleton from "@/components/profile/skeletons/bookings-skeleton";
import PaymentMethodsSkeleton from "@/components/profile/skeletons/payment-methods-skeleton";
import ProfileSkeleton from "@/components/profile/skeletons/profile-skeleton";
import SubscriptionsSkeleton from "@/components/profile/skeletons/subscriptions-skeleton";
import SubscriptionsSection from "@/components/profile/subscriptions-section";
import { useAuthStore } from "@/store/auth-store";
import React, { useEffect, useState } from "react";

const ProfilePage = () => {
  const { user } = useAuthStore();
  const [activeSection, setActiveSection] = useState("profile-section");
  const [showPhoneDialog, setShowPhoneDialog] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      setIsLoading(false);
    }
  }, [user]);

  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId);
  };

  const openEditModal = (field: string) => {
    if (field === "phone") {
      setShowPhoneDialog(true);
      return;
    }
    if (field === "email") {
      setShowEmailDialog(true);
      return;
    }
  };

  return (
    <ProtectedLayout>
      <main className="bg-background mt-24 min-h-screen md:mt-32">
        <div className="mx-4 max-w-7xl rounded-lg bg-white px-4 py-8 shadow-sm sm:px-6 md:mx-auto lg:px-8">
          <h2 className="mb-6 text-2xl font-bold text-gray-900">Setting</h2>
          <div className="flex flex-col gap-8 md:flex-row">
            <Sidebar
              activeSection={activeSection}
              handleSectionClick={handleSectionClick}
            />
            <div className="flex-1">
              {isLoading ? (
                <ProfileSkeleton />
              ) : (
                <ProfileSection
                  user={user}
                  activeSection={activeSection}
                  openEditModal={openEditModal}
                />
              )}
              {isLoading ? (
                <SubscriptionsSkeleton />
              ) : (
                <SubscriptionsSection activeSection={activeSection} />
              )}
              <FamilySection activeSection={activeSection} />
              {isLoading ? (
                <PaymentMethodsSkeleton />
              ) : (
                <PaymentMethodsSection activeSection={activeSection} />
              )}
              {isLoading ? (
                <BookingsSkeleton />
              ) : (
                <BookingsSection activeSection={activeSection} />
              )}
              <LogoutSection activeSection={activeSection} />
            </div>
          </div>
        </div>
      </main>

      <UpdatePhoneDialog
        isOpen={showPhoneDialog}
        onClose={() => setShowPhoneDialog(false)}
        onPhoneUpdated={() => {
          // TODO: Implement phone update logic
          console.log("Phone updated");
        }}
        currentPhone={user?.user_phone?.phone || ""}
        currentCountryCode={user?.user_phone?.country_code || "+1"}
      />

      <UpdateEmailDialog
        isOpen={showEmailDialog}
        onClose={() => setShowEmailDialog(false)}
        onEmailUpdated={() => {
          // TODO: Implement email update logic
          console.log("Email updated");
        }}
        currentEmail={user?.user_email?.email || ""}
      />
    </ProtectedLayout>
  );
};

export default ProfilePage;