export type SportType = "Padel" | "Pickleball" | "Tennis";
export type BookingType = "court" | "lesson" | "program" | "instructor";
export type ProgramType = "FULL" | "PER_SESSION";

export interface BookingFormData {
    court: string;
    date: string;
    time: string;
    duration: string;
    //   players: number;
    //   clutchAi: boolean;
    //   package: string;
}

export interface Court {
    id: number;
    name?: string;
    [key: string]: any; // Allow for additional properties
}